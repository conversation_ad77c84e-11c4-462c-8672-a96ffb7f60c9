"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_csv_upload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/csv-upload */ \"(app-pages-browser)/./src/components/csv-upload.tsx\");\n/* harmony import */ var _components_pnr_processor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/pnr-processor */ \"(app-pages-browser)/./src/components/pnr-processor.tsx\");\n/* harmony import */ var _components_results_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/results-table */ \"(app-pages-browser)/./src/components/results-table.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const [pnrs, setPnrs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handlePNRsExtracted = (extractedPnrs)=>{\n        setPnrs(extractedPnrs);\n        setResults([]); // Clear previous results\n    };\n    const handleProcessingComplete = (processingResults)=>{\n        setResults(processingResults);\n    };\n    const handleReset = ()=>{\n        setResults([]); // Clear the results when reset is clicked\n    };\n    const handleStop = ()=>{\n        // Optional: You could add any additional logic here when processing is stopped\n        console.log('Processing stopped by user');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Cover Genius Insurance Processor\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Upload a CSV file with PNR numbers to process insurance data and generate SQL update queries\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_csv_upload__WEBPACK_IMPORTED_MODULE_2__.CSVUpload, {\n                            onPNRsExtracted: handlePNRsExtracted\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        pnrs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pnr_processor__WEBPACK_IMPORTED_MODULE_3__.PNRProcessor, {\n                            pnrs: pnrs,\n                            onProcessingComplete: handleProcessingComplete,\n                            onReset: handleReset,\n                            onStop: handleStop\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this),\n                        results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_results_table__WEBPACK_IMPORTED_MODULE_4__.ResultsTable, {\n                            results: results\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"K91erv4Jlo3Guw5ihstgzTzMbXE=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});