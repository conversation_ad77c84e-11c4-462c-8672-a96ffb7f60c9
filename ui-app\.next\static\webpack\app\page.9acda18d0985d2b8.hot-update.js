"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/pnr-processor.tsx":
/*!******************************************!*\
  !*** ./src/components/pnr-processor.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PNRProcessor: () => (/* binding */ PNRProcessor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Play_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Play,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Play_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Play,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* __next_internal_client_entry_do_not_use__ PNRProcessor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction PNRProcessor(param) {\n    let { pnrs, onProcessingComplete, onReset, onStop } = param;\n    _s();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        pnrs: pnrs.map({\n            \"PNRProcessor.useState\": (pnr)=>({\n                    pnr,\n                    status: 'pending',\n                    progress: 0\n                })\n        }[\"PNRProcessor.useState\"]),\n        isProcessing: false,\n        completedCount: 0,\n        totalCount: pnrs.length\n    });\n    const [shouldStop, setShouldStop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const processPNR = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PNRProcessor.useCallback[processPNR]\": async (pnr)=>{\n            try {\n                var _abortControllerRef_current;\n                // Check if we should stop before starting\n                if (shouldStop) {\n                    return false;\n                }\n                // Update status to processing\n                setState({\n                    \"PNRProcessor.useCallback[processPNR]\": (prev)=>({\n                            ...prev,\n                            pnrs: prev.pnrs.map({\n                                \"PNRProcessor.useCallback[processPNR]\": (p)=>p.pnr === pnr ? {\n                                        ...p,\n                                        status: 'processing',\n                                        progress: 10\n                                    } : p\n                            }[\"PNRProcessor.useCallback[processPNR]\"])\n                        })\n                }[\"PNRProcessor.useCallback[processPNR]\"]);\n                // Simulate progress updates with stop checking\n                for(let progress = 20; progress <= 90; progress += 20){\n                    if (shouldStop) {\n                        // Mark as cancelled\n                        setState({\n                            \"PNRProcessor.useCallback[processPNR]\": (prev)=>({\n                                    ...prev,\n                                    pnrs: prev.pnrs.map({\n                                        \"PNRProcessor.useCallback[processPNR]\": (p)=>p.pnr === pnr ? {\n                                                ...p,\n                                                status: 'error',\n                                                progress: 0,\n                                                error: 'Cancelled by user'\n                                            } : p\n                                    }[\"PNRProcessor.useCallback[processPNR]\"]),\n                                    completedCount: prev.completedCount + 1\n                                })\n                        }[\"PNRProcessor.useCallback[processPNR]\"]);\n                        return false;\n                    }\n                    await new Promise({\n                        \"PNRProcessor.useCallback[processPNR]\": (resolve)=>setTimeout(resolve, 500)\n                    }[\"PNRProcessor.useCallback[processPNR]\"]);\n                    setState({\n                        \"PNRProcessor.useCallback[processPNR]\": (prev)=>({\n                                ...prev,\n                                pnrs: prev.pnrs.map({\n                                    \"PNRProcessor.useCallback[processPNR]\": (p)=>p.pnr === pnr ? {\n                                            ...p,\n                                            progress\n                                        } : p\n                                }[\"PNRProcessor.useCallback[processPNR]\"])\n                            })\n                    }[\"PNRProcessor.useCallback[processPNR]\"]);\n                }\n                // Final stop check before API call\n                if (shouldStop) {\n                    setState({\n                        \"PNRProcessor.useCallback[processPNR]\": (prev)=>({\n                                ...prev,\n                                pnrs: prev.pnrs.map({\n                                    \"PNRProcessor.useCallback[processPNR]\": (p)=>p.pnr === pnr ? {\n                                            ...p,\n                                            status: 'error',\n                                            progress: 0,\n                                            error: 'Cancelled by user'\n                                        } : p\n                                }[\"PNRProcessor.useCallback[processPNR]\"]),\n                                completedCount: prev.completedCount + 1\n                            })\n                    }[\"PNRProcessor.useCallback[processPNR]\"]);\n                    return false;\n                }\n                // Call the API with abort controller\n                const response = await fetch('/api/process-pnr', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        pnr\n                    }),\n                    signal: (_abortControllerRef_current = abortControllerRef.current) === null || _abortControllerRef_current === void 0 ? void 0 : _abortControllerRef_current.signal\n                });\n                if (!response.ok) {\n                    throw new Error(\"HTTP error! status: \".concat(response.status));\n                }\n                const result = await response.json();\n                // Update with completed status\n                setState({\n                    \"PNRProcessor.useCallback[processPNR]\": (prev)=>({\n                            ...prev,\n                            pnrs: prev.pnrs.map({\n                                \"PNRProcessor.useCallback[processPNR]\": (p)=>p.pnr === pnr ? {\n                                        ...p,\n                                        status: 'completed',\n                                        progress: 100,\n                                        result\n                                    } : p\n                            }[\"PNRProcessor.useCallback[processPNR]\"]),\n                            completedCount: prev.completedCount + 1\n                        })\n                }[\"PNRProcessor.useCallback[processPNR]\"]);\n                return true;\n            } catch (error) {\n                // Check if it was an abort error\n                if (error instanceof Error && error.name === 'AbortError') {\n                    setState({\n                        \"PNRProcessor.useCallback[processPNR]\": (prev)=>({\n                                ...prev,\n                                pnrs: prev.pnrs.map({\n                                    \"PNRProcessor.useCallback[processPNR]\": (p)=>p.pnr === pnr ? {\n                                            ...p,\n                                            status: 'error',\n                                            progress: 0,\n                                            error: 'Cancelled by user'\n                                        } : p\n                                }[\"PNRProcessor.useCallback[processPNR]\"]),\n                                completedCount: prev.completedCount + 1\n                            })\n                    }[\"PNRProcessor.useCallback[processPNR]\"]);\n                    return false;\n                }\n                // Update with error status\n                setState({\n                    \"PNRProcessor.useCallback[processPNR]\": (prev)=>({\n                            ...prev,\n                            pnrs: prev.pnrs.map({\n                                \"PNRProcessor.useCallback[processPNR]\": (p)=>p.pnr === pnr ? {\n                                        ...p,\n                                        status: 'error',\n                                        progress: 0,\n                                        error: error instanceof Error ? error.message : 'Unknown error'\n                                    } : p\n                            }[\"PNRProcessor.useCallback[processPNR]\"]),\n                            completedCount: prev.completedCount + 1\n                        })\n                }[\"PNRProcessor.useCallback[processPNR]\"]);\n                return false;\n            }\n        }\n    }[\"PNRProcessor.useCallback[processPNR]\"], [\n        shouldStop\n    ]);\n    const startProcessing = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PNRProcessor.useCallback[startProcessing]\": async ()=>{\n            // Reset stop flag and create new abort controller\n            setShouldStop(false);\n            abortControllerRef.current = new AbortController();\n            setState({\n                \"PNRProcessor.useCallback[startProcessing]\": (prev)=>({\n                        ...prev,\n                        isProcessing: true,\n                        completedCount: 0\n                    })\n            }[\"PNRProcessor.useCallback[startProcessing]\"]);\n            // Process PNRs sequentially to avoid overwhelming the APIs\n            for (const pnrRecord of state.pnrs){\n                if (pnrRecord.status === 'pending') {\n                    const success = await processPNR(pnrRecord.pnr);\n                    // If processing was stopped, break out of the loop\n                    if (!success && shouldStop) {\n                        break;\n                    }\n                }\n            }\n            // Get the final state after all processing is complete\n            setState({\n                \"PNRProcessor.useCallback[startProcessing]\": (prev)=>{\n                    const finalState = {\n                        ...prev,\n                        isProcessing: false\n                    };\n                    // Notify parent component with the completed results\n                    // Use setTimeout to ensure state update is complete\n                    setTimeout({\n                        \"PNRProcessor.useCallback[startProcessing]\": ()=>{\n                            onProcessingComplete(finalState.pnrs);\n                        }\n                    }[\"PNRProcessor.useCallback[startProcessing]\"], 0);\n                    return finalState;\n                }\n            }[\"PNRProcessor.useCallback[startProcessing]\"]);\n        }\n    }[\"PNRProcessor.useCallback[startProcessing]\"], [\n        state.pnrs,\n        processPNR,\n        onProcessingComplete,\n        shouldStop\n    ]);\n    const resetProcessing = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PNRProcessor.useCallback[resetProcessing]\": ()=>{\n            // Reset stop flag and abort any ongoing requests\n            setShouldStop(false);\n            if (abortControllerRef.current) {\n                abortControllerRef.current.abort();\n                abortControllerRef.current = null;\n            }\n            setState({\n                pnrs: pnrs.map({\n                    \"PNRProcessor.useCallback[resetProcessing]\": (pnr)=>({\n                            pnr,\n                            status: 'pending',\n                            progress: 0\n                        })\n                }[\"PNRProcessor.useCallback[resetProcessing]\"]),\n                isProcessing: false,\n                completedCount: 0,\n                totalCount: pnrs.length\n            });\n            // Notify parent component to clear results\n            if (onReset) {\n                onReset();\n            }\n        }\n    }[\"PNRProcessor.useCallback[resetProcessing]\"], [\n        pnrs,\n        onReset\n    ]);\n    const stopProcessing = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PNRProcessor.useCallback[stopProcessing]\": ()=>{\n            setShouldStop(true);\n            // Abort any ongoing API requests\n            if (abortControllerRef.current) {\n                abortControllerRef.current.abort();\n            }\n            // Notify parent component\n            if (onStop) {\n                onStop();\n            }\n        }\n    }[\"PNRProcessor.useCallback[stopProcessing]\"], [\n        onStop\n    ]);\n    const getStatusBadgeVariant = (status)=>{\n        switch(status){\n            case 'pending':\n                return 'secondary';\n            case 'processing':\n                return 'default';\n            case 'completed':\n                return 'default';\n            case 'error':\n                return 'destructive';\n            default:\n                return 'secondary';\n        }\n    };\n    const overallProgress = state.totalCount > 0 ? state.completedCount / state.totalCount * 100 : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Process PNR Records\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: startProcessing,\n                                        disabled: state.isProcessing,\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this),\n                                            state.isProcessing ? 'Processing...' : 'Start Processing'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: resetProcessing,\n                                        disabled: state.isProcessing,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Reset\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                        children: [\n                            \"Process \",\n                            state.totalCount,\n                            \" PNR records to extract insurance data\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Overall Progress\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            state.completedCount,\n                                            \" / \",\n                                            state.totalCount\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                                value: overallProgress,\n                                className: \"w-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                        children: state.pnrs.map((pnrRecord)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono font-medium\",\n                                                children: pnrRecord.pnr\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: getStatusBadgeVariant(pnrRecord.status),\n                                                children: pnrRecord.status\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            pnrRecord.status === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                                                    value: pnrRecord.progress,\n                                                    className: \"h-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 19\n                                            }, this),\n                                            pnrRecord.status === 'completed' && pnrRecord.result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-green-600\",\n                                                children: [\n                                                    pnrRecord.result.summary.missingConfirmation,\n                                                    \" missing\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this),\n                                            pnrRecord.status === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-red-600 max-w-xs truncate\",\n                                                children: pnrRecord.error\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, pnrRecord.pnr, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, this),\n                    state.completedCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                            children: [\n                                \"Completed: \",\n                                state.pnrs.filter((p)=>p.status === 'completed').length,\n                                \", Errors: \",\n                                state.pnrs.filter((p)=>p.status === 'error').length,\n                                \", Remaining: \",\n                                state.pnrs.filter((p)=>p.status === 'pending').length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Code\\\\cover-genius-v1\\\\ui-app\\\\src\\\\components\\\\pnr-processor.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, this);\n}\n_s(PNRProcessor, \"JdXR3opL5sbaO/DwSE2quOH7lrE=\");\n_c = PNRProcessor;\nvar _c;\n$RefreshReg$(_c, \"PNRProcessor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3Buci1wcm9jZXNzb3IudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNkQ7QUFDYjtBQUNpRDtBQUM3QztBQUNOO0FBQ2tCO0FBQ1Q7QUFVaEQsU0FBU2dCLGFBQWEsS0FBa0U7UUFBbEUsRUFBRUMsSUFBSSxFQUFFQyxvQkFBb0IsRUFBRUMsT0FBTyxFQUFFQyxNQUFNLEVBQXFCLEdBQWxFOztJQUMzQixNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR3JCLCtDQUFRQSxDQUF1QjtRQUN2RGdCLE1BQU1BLEtBQUtNLEdBQUc7cUNBQUNDLENBQUFBLE1BQVE7b0JBQUVBO29CQUFLQyxRQUFRO29CQUFXQyxVQUFVO2dCQUFFOztRQUM3REMsY0FBYztRQUNkQyxnQkFBZ0I7UUFDaEJDLFlBQVlaLEtBQUthLE1BQU07SUFDekI7SUFFQSxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBRy9CLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU1nQyxxQkFBcUI5Qiw2Q0FBTUEsQ0FBeUI7SUFFMUQsTUFBTStCLGFBQWFoQyxrREFBV0E7Z0RBQUMsT0FBT3NCO1lBQ3BDLElBQUk7b0JBd0RRUztnQkF2RFYsMENBQTBDO2dCQUMxQyxJQUFJRixZQUFZO29CQUNkLE9BQU87Z0JBQ1Q7Z0JBRUEsOEJBQThCO2dCQUM5QlQ7NERBQVNhLENBQUFBLE9BQVM7NEJBQ2hCLEdBQUdBLElBQUk7NEJBQ1BsQixNQUFNa0IsS0FBS2xCLElBQUksQ0FBQ00sR0FBRzt3RUFBQ2EsQ0FBQUEsSUFDbEJBLEVBQUVaLEdBQUcsS0FBS0EsTUFBTTt3Q0FBRSxHQUFHWSxDQUFDO3dDQUFFWCxRQUFRO3dDQUFjQyxVQUFVO29DQUFHLElBQUlVOzt3QkFFbkU7O2dCQUVBLCtDQUErQztnQkFDL0MsSUFBSyxJQUFJVixXQUFXLElBQUlBLFlBQVksSUFBSUEsWUFBWSxHQUFJO29CQUN0RCxJQUFJSyxZQUFZO3dCQUNkLG9CQUFvQjt3QkFDcEJUO29FQUFTYSxDQUFBQSxPQUFTO29DQUNoQixHQUFHQSxJQUFJO29DQUNQbEIsTUFBTWtCLEtBQUtsQixJQUFJLENBQUNNLEdBQUc7Z0ZBQUNhLENBQUFBLElBQ2xCQSxFQUFFWixHQUFHLEtBQUtBLE1BQU07Z0RBQUUsR0FBR1ksQ0FBQztnREFBRVgsUUFBUTtnREFBU0MsVUFBVTtnREFBR1csT0FBTzs0Q0FBb0IsSUFBSUQ7O29DQUV2RlIsZ0JBQWdCTyxLQUFLUCxjQUFjLEdBQUc7Z0NBQ3hDOzt3QkFDQSxPQUFPO29CQUNUO29CQUVBLE1BQU0sSUFBSVU7Z0VBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7O29CQUNqRGpCO2dFQUFTYSxDQUFBQSxPQUFTO2dDQUNoQixHQUFHQSxJQUFJO2dDQUNQbEIsTUFBTWtCLEtBQUtsQixJQUFJLENBQUNNLEdBQUc7NEVBQUNhLENBQUFBLElBQ2xCQSxFQUFFWixHQUFHLEtBQUtBLE1BQU07NENBQUUsR0FBR1ksQ0FBQzs0Q0FBRVY7d0NBQVMsSUFBSVU7OzRCQUV6Qzs7Z0JBQ0Y7Z0JBRUEsbUNBQW1DO2dCQUNuQyxJQUFJTCxZQUFZO29CQUNkVDtnRUFBU2EsQ0FBQUEsT0FBUztnQ0FDaEIsR0FBR0EsSUFBSTtnQ0FDUGxCLE1BQU1rQixLQUFLbEIsSUFBSSxDQUFDTSxHQUFHOzRFQUFDYSxDQUFBQSxJQUNsQkEsRUFBRVosR0FBRyxLQUFLQSxNQUFNOzRDQUFFLEdBQUdZLENBQUM7NENBQUVYLFFBQVE7NENBQVNDLFVBQVU7NENBQUdXLE9BQU87d0NBQW9CLElBQUlEOztnQ0FFdkZSLGdCQUFnQk8sS0FBS1AsY0FBYyxHQUFHOzRCQUN4Qzs7b0JBQ0EsT0FBTztnQkFDVDtnQkFFQSxxQ0FBcUM7Z0JBQ3JDLE1BQU1hLFdBQVcsTUFBTUMsTUFBTSxvQkFBb0I7b0JBQy9DQyxRQUFRO29CQUNSQyxTQUFTO3dCQUNQLGdCQUFnQjtvQkFDbEI7b0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzt3QkFBRXZCO29CQUFJO29CQUMzQndCLE1BQU0sR0FBRWYsOEJBQUFBLG1CQUFtQmdCLE9BQU8sY0FBMUJoQixrREFBQUEsNEJBQTRCZSxNQUFNO2dCQUM1QztnQkFFQSxJQUFJLENBQUNQLFNBQVNTLEVBQUUsRUFBRTtvQkFDaEIsTUFBTSxJQUFJQyxNQUFNLHVCQUF1QyxPQUFoQlYsU0FBU2hCLE1BQU07Z0JBQ3hEO2dCQUVBLE1BQU0yQixTQUFTLE1BQU1YLFNBQVNZLElBQUk7Z0JBRWxDLCtCQUErQjtnQkFDL0IvQjs0REFBU2EsQ0FBQUEsT0FBUzs0QkFDaEIsR0FBR0EsSUFBSTs0QkFDUGxCLE1BQU1rQixLQUFLbEIsSUFBSSxDQUFDTSxHQUFHO3dFQUFDYSxDQUFBQSxJQUNsQkEsRUFBRVosR0FBRyxLQUFLQSxNQUNOO3dDQUFFLEdBQUdZLENBQUM7d0NBQUVYLFFBQVE7d0NBQWFDLFVBQVU7d0NBQUswQjtvQ0FBTyxJQUNuRGhCOzs0QkFFTlIsZ0JBQWdCTyxLQUFLUCxjQUFjLEdBQUc7d0JBQ3hDOztnQkFFQSxPQUFPO1lBRVQsRUFBRSxPQUFPUyxPQUFPO2dCQUNkLGlDQUFpQztnQkFDakMsSUFBSUEsaUJBQWlCYyxTQUFTZCxNQUFNaUIsSUFBSSxLQUFLLGNBQWM7b0JBQ3pEaEM7Z0VBQVNhLENBQUFBLE9BQVM7Z0NBQ2hCLEdBQUdBLElBQUk7Z0NBQ1BsQixNQUFNa0IsS0FBS2xCLElBQUksQ0FBQ00sR0FBRzs0RUFBQ2EsQ0FBQUEsSUFDbEJBLEVBQUVaLEdBQUcsS0FBS0EsTUFDTjs0Q0FBRSxHQUFHWSxDQUFDOzRDQUFFWCxRQUFROzRDQUFTQyxVQUFVOzRDQUFHVyxPQUFPO3dDQUFvQixJQUNqRUQ7O2dDQUVOUixnQkFBZ0JPLEtBQUtQLGNBQWMsR0FBRzs0QkFDeEM7O29CQUNBLE9BQU87Z0JBQ1Q7Z0JBRUEsMkJBQTJCO2dCQUMzQk47NERBQVNhLENBQUFBLE9BQVM7NEJBQ2hCLEdBQUdBLElBQUk7NEJBQ1BsQixNQUFNa0IsS0FBS2xCLElBQUksQ0FBQ00sR0FBRzt3RUFBQ2EsQ0FBQUEsSUFDbEJBLEVBQUVaLEdBQUcsS0FBS0EsTUFDTjt3Q0FDRSxHQUFHWSxDQUFDO3dDQUNKWCxRQUFRO3dDQUNSQyxVQUFVO3dDQUNWVyxPQUFPQSxpQkFBaUJjLFFBQVFkLE1BQU1rQixPQUFPLEdBQUc7b0NBQ2xELElBQ0FuQjs7NEJBRU5SLGdCQUFnQk8sS0FBS1AsY0FBYyxHQUFHO3dCQUN4Qzs7Z0JBQ0EsT0FBTztZQUNUO1FBQ0Y7K0NBQUc7UUFBQ0c7S0FBVztJQUVmLE1BQU15QixrQkFBa0J0RCxrREFBV0E7cURBQUM7WUFDbEMsa0RBQWtEO1lBQ2xEOEIsY0FBYztZQUNkQyxtQkFBbUJnQixPQUFPLEdBQUcsSUFBSVE7WUFFakNuQzs2REFBU2EsQ0FBQUEsT0FBUzt3QkFBRSxHQUFHQSxJQUFJO3dCQUFFUixjQUFjO3dCQUFNQyxnQkFBZ0I7b0JBQUU7O1lBRW5FLDJEQUEyRDtZQUMzRCxLQUFLLE1BQU04QixhQUFhckMsTUFBTUosSUFBSSxDQUFFO2dCQUNsQyxJQUFJeUMsVUFBVWpDLE1BQU0sS0FBSyxXQUFXO29CQUNsQyxNQUFNa0MsVUFBVSxNQUFNekIsV0FBV3dCLFVBQVVsQyxHQUFHO29CQUM5QyxtREFBbUQ7b0JBQ25ELElBQUksQ0FBQ21DLFdBQVc1QixZQUFZO3dCQUMxQjtvQkFDRjtnQkFDRjtZQUNGO1lBRUEsdURBQXVEO1lBQ3ZEVDs2REFBU2EsQ0FBQUE7b0JBQ1AsTUFBTXlCLGFBQWE7d0JBQUUsR0FBR3pCLElBQUk7d0JBQUVSLGNBQWM7b0JBQU07b0JBRWxELHFEQUFxRDtvQkFDckQsb0RBQW9EO29CQUNwRGE7cUVBQVc7NEJBQ1R0QixxQkFBcUIwQyxXQUFXM0MsSUFBSTt3QkFDdEM7b0VBQUc7b0JBRUgsT0FBTzJDO2dCQUNUOztRQUNGO29EQUFHO1FBQUN2QyxNQUFNSixJQUFJO1FBQUVpQjtRQUFZaEI7UUFBc0JhO0tBQVc7SUFFN0QsTUFBTThCLGtCQUFrQjNELGtEQUFXQTtxREFBQztZQUNsQyxpREFBaUQ7WUFDakQ4QixjQUFjO1lBQ2QsSUFBSUMsbUJBQW1CZ0IsT0FBTyxFQUFFO2dCQUM5QmhCLG1CQUFtQmdCLE9BQU8sQ0FBQ2EsS0FBSztnQkFDaEM3QixtQkFBbUJnQixPQUFPLEdBQUc7WUFDL0I7WUFFQTNCLFNBQVM7Z0JBQ1BMLE1BQU1BLEtBQUtNLEdBQUc7aUVBQUNDLENBQUFBLE1BQVE7NEJBQUVBOzRCQUFLQyxRQUFROzRCQUFXQyxVQUFVO3dCQUFFOztnQkFDN0RDLGNBQWM7Z0JBQ2RDLGdCQUFnQjtnQkFDaEJDLFlBQVlaLEtBQUthLE1BQU07WUFDekI7WUFFQSwyQ0FBMkM7WUFDM0MsSUFBSVgsU0FBUztnQkFDWEE7WUFDRjtRQUNGO29EQUFHO1FBQUNGO1FBQU1FO0tBQVE7SUFFbEIsTUFBTTRDLGlCQUFpQjdELGtEQUFXQTtvREFBQztZQUNqQzhCLGNBQWM7WUFFZCxpQ0FBaUM7WUFDakMsSUFBSUMsbUJBQW1CZ0IsT0FBTyxFQUFFO2dCQUM5QmhCLG1CQUFtQmdCLE9BQU8sQ0FBQ2EsS0FBSztZQUNsQztZQUVBLDBCQUEwQjtZQUMxQixJQUFJMUMsUUFBUTtnQkFDVkE7WUFDRjtRQUNGO21EQUFHO1FBQUNBO0tBQU87SUFFWCxNQUFNNEMsd0JBQXdCLENBQUN2QztRQUM3QixPQUFRQTtZQUNOLEtBQUs7Z0JBQVcsT0FBTztZQUN2QixLQUFLO2dCQUFjLE9BQU87WUFDMUIsS0FBSztnQkFBYSxPQUFPO1lBQ3pCLEtBQUs7Z0JBQVMsT0FBTztZQUNyQjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFJQSxNQUFNd0Msa0JBQWtCNUMsTUFBTVEsVUFBVSxHQUFHLElBQ3ZDLE1BQU9ELGNBQWMsR0FBR1AsTUFBTVEsVUFBVSxHQUFJLE1BQzVDO0lBRUoscUJBQ0UsOERBQUN4QixxREFBSUE7UUFBQzZELFdBQVU7OzBCQUNkLDhEQUFDMUQsMkRBQVVBOztrQ0FDVCw4REFBQ0MsMERBQVNBO3dCQUFDeUQsV0FBVTs7MENBQ25CLDhEQUFDQzswQ0FBSzs7Ozs7OzBDQUNOLDhEQUFDQztnQ0FBSUYsV0FBVTs7a0RBQ2IsOERBQUM5RCx5REFBTUE7d0NBQ0xpRSxTQUFTYjt3Q0FDVGMsVUFBVWpELE1BQU1NLFlBQVk7d0NBQzVCNEMsTUFBSzs7MERBRUwsOERBQUN6RCwwRkFBSUE7Z0RBQUNvRCxXQUFVOzs7Ozs7NENBQ2Y3QyxNQUFNTSxZQUFZLEdBQUcsa0JBQWtCOzs7Ozs7O2tEQUUxQyw4REFBQ3ZCLHlEQUFNQTt3Q0FDTGlFLFNBQVNSO3dDQUNUUyxVQUFVakQsTUFBTU0sWUFBWTt3Q0FDNUI2QyxTQUFRO3dDQUNSRCxNQUFLOzswREFFTCw4REFBQ3hELDBGQUFTQTtnREFBQ21ELFdBQVU7Ozs7Ozs0Q0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSzVDLDhEQUFDM0QsZ0VBQWVBOzs0QkFBQzs0QkFDTmMsTUFBTVEsVUFBVTs0QkFBQzs7Ozs7Ozs7Ozs7OzswQkFHOUIsOERBQUN2Qiw0REFBV0E7Z0JBQUM0RCxXQUFVOztrQ0FFckIsOERBQUNFO3dCQUFJRixXQUFVOzswQ0FDYiw4REFBQ0U7Z0NBQUlGLFdBQVU7O2tEQUNiLDhEQUFDQztrREFBSzs7Ozs7O2tEQUNOLDhEQUFDQTs7NENBQU05QyxNQUFNTyxjQUFjOzRDQUFDOzRDQUFJUCxNQUFNUSxVQUFVOzs7Ozs7Ozs7Ozs7OzBDQUVsRCw4REFBQ25CLDZEQUFRQTtnQ0FBQytELE9BQU9SO2dDQUFpQkMsV0FBVTs7Ozs7Ozs7Ozs7O2tDQUk5Qyw4REFBQ0U7d0JBQUlGLFdBQVU7a0NBQ1o3QyxNQUFNSixJQUFJLENBQUNNLEdBQUcsQ0FBQyxDQUFDbUMsMEJBQ2YsOERBQUNVO2dDQUVDRixXQUFVOztrREFFViw4REFBQ0U7d0NBQUlGLFdBQVU7OzBEQUNiLDhEQUFDQztnREFBS0QsV0FBVTswREFBeUJSLFVBQVVsQyxHQUFHOzs7Ozs7MERBQ3RELDhEQUFDYix1REFBS0E7Z0RBQUM2RCxTQUFTUixzQkFBc0JOLFVBQVVqQyxNQUFNOzBEQUNuRGlDLFVBQVVqQyxNQUFNOzs7Ozs7Ozs7Ozs7a0RBSXJCLDhEQUFDMkM7d0NBQUlGLFdBQVU7OzRDQUNaUixVQUFVakMsTUFBTSxLQUFLLDhCQUNwQiw4REFBQzJDO2dEQUFJRixXQUFVOzBEQUNiLDRFQUFDeEQsNkRBQVFBO29EQUFDK0QsT0FBT2YsVUFBVWhDLFFBQVE7b0RBQUV3QyxXQUFVOzs7Ozs7Ozs7Ozs0Q0FJbERSLFVBQVVqQyxNQUFNLEtBQUssZUFBZWlDLFVBQVVOLE1BQU0sa0JBQ25ELDhEQUFDZTtnREFBS0QsV0FBVTs7b0RBQ2JSLFVBQVVOLE1BQU0sQ0FBQ3NCLE9BQU8sQ0FBQ0MsbUJBQW1CO29EQUFDOzs7Ozs7OzRDQUlqRGpCLFVBQVVqQyxNQUFNLEtBQUsseUJBQ3BCLDhEQUFDMEM7Z0RBQUtELFdBQVU7MERBQ2JSLFVBQVVyQixLQUFLOzs7Ozs7Ozs7Ozs7OytCQXpCakJxQixVQUFVbEMsR0FBRzs7Ozs7Ozs7OztvQkFrQ3ZCSCxNQUFNTyxjQUFjLEdBQUcsbUJBQ3RCLDhEQUFDaEIsdURBQUtBO2tDQUNKLDRFQUFDQyxrRUFBZ0JBOztnQ0FBQztnQ0FDSlEsTUFBTUosSUFBSSxDQUFDMkQsTUFBTSxDQUFDeEMsQ0FBQUEsSUFBS0EsRUFBRVgsTUFBTSxLQUFLLGFBQWFLLE1BQU07Z0NBQUM7Z0NBQzNEVCxNQUFNSixJQUFJLENBQUMyRCxNQUFNLENBQUN4QyxDQUFBQSxJQUFLQSxFQUFFWCxNQUFNLEtBQUssU0FBU0ssTUFBTTtnQ0FBQztnQ0FDakRULE1BQU1KLElBQUksQ0FBQzJELE1BQU0sQ0FBQ3hDLENBQUFBLElBQUtBLEVBQUVYLE1BQU0sS0FBSyxXQUFXSyxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPL0U7R0F4U2dCZDtLQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiYXNpbC5oYXJyaXNvblxcRG9jdW1lbnRzXFxDb2RlXFxjb3Zlci1nZW5pdXMtdjFcXHVpLWFwcFxcc3JjXFxjb21wb25lbnRzXFxwbnItcHJvY2Vzc29yLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlQ2FsbGJhY2ssIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nO1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmREZXNjcmlwdGlvbiwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnO1xuaW1wb3J0IHsgUHJvZ3Jlc3MgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvcHJvZ3Jlc3MnO1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnO1xuaW1wb3J0IHsgQWxlcnQsIEFsZXJ0RGVzY3JpcHRpb24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYWxlcnQnO1xuaW1wb3J0IHsgUGxheSwgUm90YXRlQ2N3LCBTcXVhcmUgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgUE5SUmVjb3JkLCBCYXRjaFByb2Nlc3NpbmdTdGF0ZSB9IGZyb20gJ0AvdHlwZXMnO1xuXG5pbnRlcmZhY2UgUE5SUHJvY2Vzc29yUHJvcHMge1xuICBwbnJzOiBzdHJpbmdbXTtcbiAgb25Qcm9jZXNzaW5nQ29tcGxldGU6IChyZXN1bHRzOiBQTlJSZWNvcmRbXSkgPT4gdm9pZDtcbiAgb25SZXNldD86ICgpID0+IHZvaWQ7XG4gIG9uU3RvcD86ICgpID0+IHZvaWQ7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBQTlJQcm9jZXNzb3IoeyBwbnJzLCBvblByb2Nlc3NpbmdDb21wbGV0ZSwgb25SZXNldCwgb25TdG9wIH06IFBOUlByb2Nlc3NvclByb3BzKSB7XG4gIGNvbnN0IFtzdGF0ZSwgc2V0U3RhdGVdID0gdXNlU3RhdGU8QmF0Y2hQcm9jZXNzaW5nU3RhdGU+KHtcbiAgICBwbnJzOiBwbnJzLm1hcChwbnIgPT4gKHsgcG5yLCBzdGF0dXM6ICdwZW5kaW5nJywgcHJvZ3Jlc3M6IDAgfSkpLFxuICAgIGlzUHJvY2Vzc2luZzogZmFsc2UsXG4gICAgY29tcGxldGVkQ291bnQ6IDAsXG4gICAgdG90YWxDb3VudDogcG5ycy5sZW5ndGhcbiAgfSk7XG5cbiAgY29uc3QgW3Nob3VsZFN0b3AsIHNldFNob3VsZFN0b3BdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBhYm9ydENvbnRyb2xsZXJSZWYgPSB1c2VSZWY8QWJvcnRDb250cm9sbGVyIHwgbnVsbD4obnVsbCk7XG5cbiAgY29uc3QgcHJvY2Vzc1BOUiA9IHVzZUNhbGxiYWNrKGFzeW5jIChwbnI6IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4gPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBDaGVjayBpZiB3ZSBzaG91bGQgc3RvcCBiZWZvcmUgc3RhcnRpbmdcbiAgICAgIGlmIChzaG91bGRTdG9wKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgLy8gVXBkYXRlIHN0YXR1cyB0byBwcm9jZXNzaW5nXG4gICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7XG4gICAgICAgIC4uLnByZXYsXG4gICAgICAgIHBucnM6IHByZXYucG5ycy5tYXAocCA9PlxuICAgICAgICAgIHAucG5yID09PSBwbnIgPyB7IC4uLnAsIHN0YXR1czogJ3Byb2Nlc3NpbmcnLCBwcm9ncmVzczogMTAgfSA6IHBcbiAgICAgICAgKVxuICAgICAgfSkpO1xuXG4gICAgICAvLyBTaW11bGF0ZSBwcm9ncmVzcyB1cGRhdGVzIHdpdGggc3RvcCBjaGVja2luZ1xuICAgICAgZm9yIChsZXQgcHJvZ3Jlc3MgPSAyMDsgcHJvZ3Jlc3MgPD0gOTA7IHByb2dyZXNzICs9IDIwKSB7XG4gICAgICAgIGlmIChzaG91bGRTdG9wKSB7XG4gICAgICAgICAgLy8gTWFyayBhcyBjYW5jZWxsZWRcbiAgICAgICAgICBzZXRTdGF0ZShwcmV2ID0+ICh7XG4gICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgcG5yczogcHJldi5wbnJzLm1hcChwID0+XG4gICAgICAgICAgICAgIHAucG5yID09PSBwbnIgPyB7IC4uLnAsIHN0YXR1czogJ2Vycm9yJywgcHJvZ3Jlc3M6IDAsIGVycm9yOiAnQ2FuY2VsbGVkIGJ5IHVzZXInIH0gOiBwXG4gICAgICAgICAgICApLFxuICAgICAgICAgICAgY29tcGxldGVkQ291bnQ6IHByZXYuY29tcGxldGVkQ291bnQgKyAxXG4gICAgICAgICAgfSkpO1xuICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuXG4gICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCA1MDApKTtcbiAgICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgcG5yczogcHJldi5wbnJzLm1hcChwID0+XG4gICAgICAgICAgICBwLnBuciA9PT0gcG5yID8geyAuLi5wLCBwcm9ncmVzcyB9IDogcFxuICAgICAgICAgIClcbiAgICAgICAgfSkpO1xuICAgICAgfVxuXG4gICAgICAvLyBGaW5hbCBzdG9wIGNoZWNrIGJlZm9yZSBBUEkgY2FsbFxuICAgICAgaWYgKHNob3VsZFN0b3ApIHtcbiAgICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgcG5yczogcHJldi5wbnJzLm1hcChwID0+XG4gICAgICAgICAgICBwLnBuciA9PT0gcG5yID8geyAuLi5wLCBzdGF0dXM6ICdlcnJvcicsIHByb2dyZXNzOiAwLCBlcnJvcjogJ0NhbmNlbGxlZCBieSB1c2VyJyB9IDogcFxuICAgICAgICAgICksXG4gICAgICAgICAgY29tcGxldGVkQ291bnQ6IHByZXYuY29tcGxldGVkQ291bnQgKyAxXG4gICAgICAgIH0pKTtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuXG4gICAgICAvLyBDYWxsIHRoZSBBUEkgd2l0aCBhYm9ydCBjb250cm9sbGVyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3Byb2Nlc3MtcG5yJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgcG5yIH0pLFxuICAgICAgICBzaWduYWw6IGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50Py5zaWduYWwsXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEhUVFAgZXJyb3IhIHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgICAgLy8gVXBkYXRlIHdpdGggY29tcGxldGVkIHN0YXR1c1xuICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBwbnJzOiBwcmV2LnBucnMubWFwKHAgPT5cbiAgICAgICAgICBwLnBuciA9PT0gcG5yXG4gICAgICAgICAgICA/IHsgLi4ucCwgc3RhdHVzOiAnY29tcGxldGVkJywgcHJvZ3Jlc3M6IDEwMCwgcmVzdWx0IH1cbiAgICAgICAgICAgIDogcFxuICAgICAgICApLFxuICAgICAgICBjb21wbGV0ZWRDb3VudDogcHJldi5jb21wbGV0ZWRDb3VudCArIDFcbiAgICAgIH0pKTtcblxuICAgICAgcmV0dXJuIHRydWU7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgLy8gQ2hlY2sgaWYgaXQgd2FzIGFuIGFib3J0IGVycm9yXG4gICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvciAmJiBlcnJvci5uYW1lID09PSAnQWJvcnRFcnJvcicpIHtcbiAgICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgcG5yczogcHJldi5wbnJzLm1hcChwID0+XG4gICAgICAgICAgICBwLnBuciA9PT0gcG5yXG4gICAgICAgICAgICAgID8geyAuLi5wLCBzdGF0dXM6ICdlcnJvcicsIHByb2dyZXNzOiAwLCBlcnJvcjogJ0NhbmNlbGxlZCBieSB1c2VyJyB9XG4gICAgICAgICAgICAgIDogcFxuICAgICAgICAgICksXG4gICAgICAgICAgY29tcGxldGVkQ291bnQ6IHByZXYuY29tcGxldGVkQ291bnQgKyAxXG4gICAgICAgIH0pKTtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuXG4gICAgICAvLyBVcGRhdGUgd2l0aCBlcnJvciBzdGF0dXNcbiAgICAgIHNldFN0YXRlKHByZXYgPT4gKHtcbiAgICAgICAgLi4ucHJldixcbiAgICAgICAgcG5yczogcHJldi5wbnJzLm1hcChwID0+XG4gICAgICAgICAgcC5wbnIgPT09IHBuclxuICAgICAgICAgICAgPyB7XG4gICAgICAgICAgICAgICAgLi4ucCxcbiAgICAgICAgICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgICAgICAgICAgcHJvZ3Jlc3M6IDAsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDogcFxuICAgICAgICApLFxuICAgICAgICBjb21wbGV0ZWRDb3VudDogcHJldi5jb21wbGV0ZWRDb3VudCArIDFcbiAgICAgIH0pKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH0sIFtzaG91bGRTdG9wXSk7XG5cbiAgY29uc3Qgc3RhcnRQcm9jZXNzaW5nID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIC8vIFJlc2V0IHN0b3AgZmxhZyBhbmQgY3JlYXRlIG5ldyBhYm9ydCBjb250cm9sbGVyXG4gICAgc2V0U2hvdWxkU3RvcChmYWxzZSk7XG4gICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG5cbiAgICBzZXRTdGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIGlzUHJvY2Vzc2luZzogdHJ1ZSwgY29tcGxldGVkQ291bnQ6IDAgfSkpO1xuXG4gICAgLy8gUHJvY2VzcyBQTlJzIHNlcXVlbnRpYWxseSB0byBhdm9pZCBvdmVyd2hlbG1pbmcgdGhlIEFQSXNcbiAgICBmb3IgKGNvbnN0IHBuclJlY29yZCBvZiBzdGF0ZS5wbnJzKSB7XG4gICAgICBpZiAocG5yUmVjb3JkLnN0YXR1cyA9PT0gJ3BlbmRpbmcnKSB7XG4gICAgICAgIGNvbnN0IHN1Y2Nlc3MgPSBhd2FpdCBwcm9jZXNzUE5SKHBuclJlY29yZC5wbnIpO1xuICAgICAgICAvLyBJZiBwcm9jZXNzaW5nIHdhcyBzdG9wcGVkLCBicmVhayBvdXQgb2YgdGhlIGxvb3BcbiAgICAgICAgaWYgKCFzdWNjZXNzICYmIHNob3VsZFN0b3ApIHtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEdldCB0aGUgZmluYWwgc3RhdGUgYWZ0ZXIgYWxsIHByb2Nlc3NpbmcgaXMgY29tcGxldGVcbiAgICBzZXRTdGF0ZShwcmV2ID0+IHtcbiAgICAgIGNvbnN0IGZpbmFsU3RhdGUgPSB7IC4uLnByZXYsIGlzUHJvY2Vzc2luZzogZmFsc2UgfTtcblxuICAgICAgLy8gTm90aWZ5IHBhcmVudCBjb21wb25lbnQgd2l0aCB0aGUgY29tcGxldGVkIHJlc3VsdHNcbiAgICAgIC8vIFVzZSBzZXRUaW1lb3V0IHRvIGVuc3VyZSBzdGF0ZSB1cGRhdGUgaXMgY29tcGxldGVcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBvblByb2Nlc3NpbmdDb21wbGV0ZShmaW5hbFN0YXRlLnBucnMpO1xuICAgICAgfSwgMCk7XG5cbiAgICAgIHJldHVybiBmaW5hbFN0YXRlO1xuICAgIH0pO1xuICB9LCBbc3RhdGUucG5ycywgcHJvY2Vzc1BOUiwgb25Qcm9jZXNzaW5nQ29tcGxldGUsIHNob3VsZFN0b3BdKTtcblxuICBjb25zdCByZXNldFByb2Nlc3NpbmcgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgLy8gUmVzZXQgc3RvcCBmbGFnIGFuZCBhYm9ydCBhbnkgb25nb2luZyByZXF1ZXN0c1xuICAgIHNldFNob3VsZFN0b3AoZmFsc2UpO1xuICAgIGlmIChhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudCkge1xuICAgICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQuYWJvcnQoKTtcbiAgICAgIGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50ID0gbnVsbDtcbiAgICB9XG5cbiAgICBzZXRTdGF0ZSh7XG4gICAgICBwbnJzOiBwbnJzLm1hcChwbnIgPT4gKHsgcG5yLCBzdGF0dXM6ICdwZW5kaW5nJywgcHJvZ3Jlc3M6IDAgfSkpLFxuICAgICAgaXNQcm9jZXNzaW5nOiBmYWxzZSxcbiAgICAgIGNvbXBsZXRlZENvdW50OiAwLFxuICAgICAgdG90YWxDb3VudDogcG5ycy5sZW5ndGhcbiAgICB9KTtcblxuICAgIC8vIE5vdGlmeSBwYXJlbnQgY29tcG9uZW50IHRvIGNsZWFyIHJlc3VsdHNcbiAgICBpZiAob25SZXNldCkge1xuICAgICAgb25SZXNldCgpO1xuICAgIH1cbiAgfSwgW3BucnMsIG9uUmVzZXRdKTtcblxuICBjb25zdCBzdG9wUHJvY2Vzc2luZyA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBzZXRTaG91bGRTdG9wKHRydWUpO1xuXG4gICAgLy8gQWJvcnQgYW55IG9uZ29pbmcgQVBJIHJlcXVlc3RzXG4gICAgaWYgKGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50KSB7XG4gICAgICBhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudC5hYm9ydCgpO1xuICAgIH1cblxuICAgIC8vIE5vdGlmeSBwYXJlbnQgY29tcG9uZW50XG4gICAgaWYgKG9uU3RvcCkge1xuICAgICAgb25TdG9wKCk7XG4gICAgfVxuICB9LCBbb25TdG9wXSk7XG5cbiAgY29uc3QgZ2V0U3RhdHVzQmFkZ2VWYXJpYW50ID0gKHN0YXR1czogUE5SUmVjb3JkWydzdGF0dXMnXSkgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdwZW5kaW5nJzogcmV0dXJuICdzZWNvbmRhcnknO1xuICAgICAgY2FzZSAncHJvY2Vzc2luZyc6IHJldHVybiAnZGVmYXVsdCc7XG4gICAgICBjYXNlICdjb21wbGV0ZWQnOiByZXR1cm4gJ2RlZmF1bHQnO1xuICAgICAgY2FzZSAnZXJyb3InOiByZXR1cm4gJ2Rlc3RydWN0aXZlJztcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAnc2Vjb25kYXJ5JztcbiAgICB9XG4gIH07XG5cblxuXG4gIGNvbnN0IG92ZXJhbGxQcm9ncmVzcyA9IHN0YXRlLnRvdGFsQ291bnQgPiAwXG4gICAgPyAoc3RhdGUuY29tcGxldGVkQ291bnQgLyBzdGF0ZS50b3RhbENvdW50KSAqIDEwMFxuICAgIDogMDtcblxuICByZXR1cm4gKFxuICAgIDxDYXJkIGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgPHNwYW4+UHJvY2VzcyBQTlIgUmVjb3Jkczwvc3Bhbj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17c3RhcnRQcm9jZXNzaW5nfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17c3RhdGUuaXNQcm9jZXNzaW5nfVxuICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8UGxheSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICB7c3RhdGUuaXNQcm9jZXNzaW5nID8gJ1Byb2Nlc3NpbmcuLi4nIDogJ1N0YXJ0IFByb2Nlc3NpbmcnfVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e3Jlc2V0UHJvY2Vzc2luZ31cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e3N0YXRlLmlzUHJvY2Vzc2luZ31cbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8Um90YXRlQ2N3IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgIFJlc2V0XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgUHJvY2VzcyB7c3RhdGUudG90YWxDb3VudH0gUE5SIHJlY29yZHMgdG8gZXh0cmFjdCBpbnN1cmFuY2UgZGF0YVxuICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgey8qIE92ZXJhbGwgUHJvZ3Jlc3MgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtXCI+XG4gICAgICAgICAgICA8c3Bhbj5PdmVyYWxsIFByb2dyZXNzPC9zcGFuPlxuICAgICAgICAgICAgPHNwYW4+e3N0YXRlLmNvbXBsZXRlZENvdW50fSAvIHtzdGF0ZS50b3RhbENvdW50fTwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8UHJvZ3Jlc3MgdmFsdWU9e292ZXJhbGxQcm9ncmVzc30gY2xhc3NOYW1lPVwidy1mdWxsXCIgLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEluZGl2aWR1YWwgUE5SIFN0YXR1cyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgbWF4LWgtOTYgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAge3N0YXRlLnBucnMubWFwKChwbnJSZWNvcmQpID0+IChcbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAga2V5PXtwbnJSZWNvcmQucG5yfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC0zIGJvcmRlciByb3VuZGVkLWxnXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbW9ubyBmb250LW1lZGl1bVwiPntwbnJSZWNvcmQucG5yfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD17Z2V0U3RhdHVzQmFkZ2VWYXJpYW50KHBuclJlY29yZC5zdGF0dXMpfT5cbiAgICAgICAgICAgICAgICAgIHtwbnJSZWNvcmQuc3RhdHVzfVxuICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgICAgICAgICB7cG5yUmVjb3JkLnN0YXR1cyA9PT0gJ3Byb2Nlc3NpbmcnICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yNFwiPlxuICAgICAgICAgICAgICAgICAgICA8UHJvZ3Jlc3MgdmFsdWU9e3BuclJlY29yZC5wcm9ncmVzc30gY2xhc3NOYW1lPVwiaC0yXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICB7cG5yUmVjb3JkLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcgJiYgcG5yUmVjb3JkLnJlc3VsdCAmJiAoXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JlZW4tNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtwbnJSZWNvcmQucmVzdWx0LnN1bW1hcnkubWlzc2luZ0NvbmZpcm1hdGlvbn0gbWlzc2luZ1xuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICB7cG5yUmVjb3JkLnN0YXR1cyA9PT0gJ2Vycm9yJyAmJiAoXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTYwMCBtYXgtdy14cyB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgICAgICB7cG5yUmVjb3JkLmVycm9yfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBTdW1tYXJ5ICovfVxuICAgICAgICB7c3RhdGUuY29tcGxldGVkQ291bnQgPiAwICYmIChcbiAgICAgICAgICA8QWxlcnQ+XG4gICAgICAgICAgICA8QWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgQ29tcGxldGVkOiB7c3RhdGUucG5ycy5maWx0ZXIocCA9PiBwLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcpLmxlbmd0aH0sXG4gICAgICAgICAgICAgIEVycm9yczoge3N0YXRlLnBucnMuZmlsdGVyKHAgPT4gcC5zdGF0dXMgPT09ICdlcnJvcicpLmxlbmd0aH0sXG4gICAgICAgICAgICAgIFJlbWFpbmluZzoge3N0YXRlLnBucnMuZmlsdGVyKHAgPT4gcC5zdGF0dXMgPT09ICdwZW5kaW5nJykubGVuZ3RofVxuICAgICAgICAgICAgPC9BbGVydERlc2NyaXB0aW9uPlxuICAgICAgICAgIDwvQWxlcnQ+XG4gICAgICAgICl9XG4gICAgICA8L0NhcmRDb250ZW50PlxuICAgIDwvQ2FyZD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VSZWYiLCJCdXR0b24iLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiUHJvZ3Jlc3MiLCJCYWRnZSIsIkFsZXJ0IiwiQWxlcnREZXNjcmlwdGlvbiIsIlBsYXkiLCJSb3RhdGVDY3ciLCJQTlJQcm9jZXNzb3IiLCJwbnJzIiwib25Qcm9jZXNzaW5nQ29tcGxldGUiLCJvblJlc2V0Iiwib25TdG9wIiwic3RhdGUiLCJzZXRTdGF0ZSIsIm1hcCIsInBuciIsInN0YXR1cyIsInByb2dyZXNzIiwiaXNQcm9jZXNzaW5nIiwiY29tcGxldGVkQ291bnQiLCJ0b3RhbENvdW50IiwibGVuZ3RoIiwic2hvdWxkU3RvcCIsInNldFNob3VsZFN0b3AiLCJhYm9ydENvbnRyb2xsZXJSZWYiLCJwcm9jZXNzUE5SIiwicHJldiIsInAiLCJlcnJvciIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJzaWduYWwiLCJjdXJyZW50Iiwib2siLCJFcnJvciIsInJlc3VsdCIsImpzb24iLCJuYW1lIiwibWVzc2FnZSIsInN0YXJ0UHJvY2Vzc2luZyIsIkFib3J0Q29udHJvbGxlciIsInBuclJlY29yZCIsInN1Y2Nlc3MiLCJmaW5hbFN0YXRlIiwicmVzZXRQcm9jZXNzaW5nIiwiYWJvcnQiLCJzdG9wUHJvY2Vzc2luZyIsImdldFN0YXR1c0JhZGdlVmFyaWFudCIsIm92ZXJhbGxQcm9ncmVzcyIsImNsYXNzTmFtZSIsInNwYW4iLCJkaXYiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJzaXplIiwidmFyaWFudCIsInZhbHVlIiwic3VtbWFyeSIsIm1pc3NpbmdDb25maXJtYXRpb24iLCJmaWx0ZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/pnr-processor.tsx\n"));

/***/ })

});