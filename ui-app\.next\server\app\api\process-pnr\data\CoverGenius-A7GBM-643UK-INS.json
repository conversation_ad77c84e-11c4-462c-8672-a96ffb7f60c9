{"id": "A7GBM-643UK-INS", "status": "CONFIRMED", "currency": "AED", "total_price": 71.4, "total_price_formatted": "AED71.40", "partner_transaction_id": null, "created_at": "2025-05-18T18:03:53.537594Z", "updated_at": "2025-05-18T18:39:08.877539Z", "pds_url": "https://www.xcover.com/en/pds/A7GBM-643UK-INS", "security_token": "vxWHu-RH7Py-ZjrrS-sC6iI", "quotes": [{"id": "a85085fe-1eb1-4fcf-a4f4-ebd52f35adbc", "policy_start_date": "2025-05-18T18:03:53.525970+00:00", "policy_end_date": "2025-06-09T20:35:00+04:00", "status": "CONFIRMED", "price": 71.4, "price_formatted": "AED71.40", "policy": {"policy_type": "comprehensive_travel_insurance", "policy_type_version": "9", "policy_type_slug": "comprehensive_travel_insurance_v9", "policy_type_group_name": "travel", "policy_name": "FlyDubai - Comprehensive (ROW)", "policy_code": "00000000", "policy_version": "ee55df79-2855-4150-8ac8-bb9bfe56fd2b", "category": "comprehensive_travel_insurance", "content": {"title": "Comprehensive Travel Insurance", "header": null, "description": "n/a", "optout_msg": "", "inclusions": [], "exclusions": [], "disclaimer": "This policy is underwritten by Dubai Insurance Company and powered by XCover.com. By purchasing a policy, you agree that you are 84 years of age or below and that you have read and agree to the Terms & Conditions.", "disclaimer_html": "<p data-block-key=\"flhma\">This policy is underwritten by Dubai Insurance Company and powered by XCover.com. By purchasing a policy, you agree that you are 84 years of age or below and that you have read and agree to the <a href=\"https://www.xcover.com/en/pds/flydubai-comprehensive-travel-insurance\" rel=\"noopener noreferrer\" target=\"_blank\">Terms & Conditions</a>.</p>", "payment_disclaimer": "", "in_path_disclaimer": "", "extra_content": {}}, "underwriter": {"disclaimer": "Dubai Insurance Company", "name": "Dubai Insurance Company"}, "claim_selector_id": "eeafddeb-75a6-4381-beb5-8a7bd9731f38", "policy_currency": "AED"}, "insured": [{"id": "7099f773-86db-486a-baf7-6518bbf48c0c", "first_name": "FIZA", "last_name": "AKRAM", "region": null}, {"id": "ff3d4c18-dffc-4a05-9f44-b5ef801c4958", "first_name": "KHALAF", "last_name": "AL GHAITH", "region": null}], "tax": {"total_tax": 3.4, "total_amount_without_tax": 68, "total_tax_formatted": "AED3.40", "total_amount_without_tax_formatted": "AED68.00", "taxes": [{"tax_amount": 3.4, "tax_code": "VAT", "tax_amount_formatted": "AED3.40"}]}, "duration": "21 22:31:06.474030", "benefits": [{"benefit_content_id": "e70c0627-e64b-4de3-8042-d0d10dcafab7", "description": "Medical Expenses Including Transportation, Evacuation and Repatriation of Mortal Remains*", "limit_description": "50,000 USD", "excess_description": "100 USD"}, {"benefit_content_id": "c3407f6c-3219-4497-96a3-5fe188ee91f2", "description": "Trip Cancellation", "limit_description": "1,000 USD"}, {"benefit_content_id": "39b97cf8-00cd-4040-b1b1-ba13e929b167", "description": "Trip Curtailment", "limit_description": "1,000 USD"}, {"benefit_content_id": "d80cd3f0-73fa-44b0-b5b0-f0c899439ae5", "description": "<PERSON> (Beyond 4 Hours)", "limit_description": "500 USD Max Limit, 50 USD per Hour Delay"}, {"benefit_content_id": "fd360a35-9367-4607-b059-f1cb7f80dfd8", "description": "Personal Accident - Accidental Death, Permanent Total Disability, Permanent Partial Disability", "limit_description": "25,000 USD"}, {"benefit_content_id": "ff5a1288-5120-48ed-a9e8-b8faaa82438d", "description": "Personal Liability", "limit_description": "50,000 USD"}, {"benefit_content_id": "d4dad855-d2ca-4ef5-a0bb-e41c5af60593", "description": "Compassionate Visit", "limit_description": "Return economy class airfare and accomodation for one accompanying person"}, {"benefit_content_id": "944ddfe8-a745-48c8-9911-29791c0111f6", "description": "Loss of Baggage, Personal Effects & Travel Documents", "limit_description": "1,000 USD"}, {"benefit_content_id": "5e2b5cc1-3e84-4ca4-9df5-9981be08650e", "description": "Single Item Limit", "parent_benefit_content_id": "944ddfe8-a745-48c8-9911-29791c0111f6", "parent_description": "Loss of Baggage, Personal Effects & Travel Documents", "limit_description": "250 USD"}, {"benefit_content_id": "6f5dc062-4552-4d6d-9c72-0ed7c0d977ca", "description": "Valuables limit", "parent_benefit_content_id": "944ddfe8-a745-48c8-9911-29791c0111f6", "parent_description": "Loss of Baggage, Personal Effects & Travel Documents", "limit_description": "500 USD"}, {"benefit_content_id": "10ab54b8-fda8-4277-99d8-be0f981d54cc", "description": "Baggage Arrival <PERSON> (Beyond 4 Hours)", "limit_description": "150 USD"}, {"benefit_content_id": "0e16a10a-2891-430d-91b3-37f68cca9de5", "description": "Missed Departure", "limit_description": "150 USD"}, {"benefit_content_id": "a994b70b-4d25-4a0e-9df7-9dedbf03082f", "description": "Legal Fees", "limit_description": "2,000 USD"}, {"benefit_content_id": "ff970ab2-d20e-429f-8df5-876f8d4361f7", "description": "Accidental Dental", "limit_description": "500 USD", "excess_description": "50 USD"}], "commission": {"partner_commission": 38.08, "partner_commission_formatted": "AED38.08", "surcharge_commission": 0, "surcharge_commission_formatted": "AED0.00", "total_commission": 38.08, "total_commission_formatted": "AED38.08"}, "created_at": "2025-05-18T18:03:53.525970Z", "confirmed_at": "2025-05-18T18:39:06.386423Z", "updated_at": "2025-05-18T18:39:06.598998Z", "cancelled_at": null, "is_renewable": false, "is_pricebeat_enabled": null, "cover_amount": null, "cover_amount_formatted": null, "pds_url": "https://www.xcover.com/en/pds/A7GBM-643UK-INS?policy_type=comprehensive_travel_insurance_v9", "attachments": [], "files": [], "custom_documents": null, "extra_fields": {"trip_duration": 7, "usd": {"fx": "0.27", "tax": "0.93", "premium": "19.44"}}, "surcharge": {"total_amount": null, "total_amount_formatted": null, "surcharges": null}, "parent_quote_status": null, "experiment": null, "next_renewal": null, "can_be_cancelled": true, "third_party_admins": [], "ombudsman_list": []}], "coi": {"url": "https://www.xcover.com/en/coi/A7GBM-643UK-INS?security_token=vxWHu-RH7Py-ZjrrS-sC6iI", "pdf": "https://www.xcover.com/en/coi/A7GBM-643UK-INS.pdf?security_token=vxWHu-RH7Py-ZjrrS-sC6iI"}, "account_url": "https://www.xcover.com/en/account?id=822f8543-f398-411d-adbf-c7bc1cac3fb3&signup_token=Rr9J0-1QQSO-aVPGu-3darr&region=eu-central-1", "sign_up_url": "https://www.xcover.com/en/account?id=822f8543-f398-411d-adbf-c7bc1cac3fb3&signup_token=Rr9J0-1QQSO-aVPGu-3darr&region=eu-central-1", "policyholder": {"first_name": "KHALAF", "last_name": "AL GHAITH", "email": "<EMAIL>", "phone": null, "address1": null, "address2": null, "postcode": null, "company": null, "company_reg_id": null, "middle_name": null, "country": "AE", "age": null, "city": null, "region": null, "secondary_email": null, "birth_date": null, "allow_updates": true, "fields_allowed_to_update": ["phone", "last_name", "city", "birth_date", "postcode", "middle_name", "company", "age", "address1", "address2", "company_reg_id", "region", "secondary_email", "first_name", "email", "tax_payer_id"]}, "total_tax": 3.4, "total_tax_formatted": "AED3.40", "total_premium": 68, "total_premium_formatted": "AED68.00", "fnol_link": "https://www.xcover.com/en/account/claims/fnol?bookingID=A7GBM-643UK-INS&security_token=vxWHu-RH7Py-ZjrrS-sC6iI", "booking_agent": null, "partner": {"id": "ZDTIY", "slug": "fly<PERSON><PERSON><PERSON>", "name": "fly<PERSON><PERSON><PERSON>", "title": "fly<PERSON><PERSON><PERSON>", "logo": "https://static.xcover.com/media/partnerlogos/2025/03/25/image_35_new_2.png", "contact_url": "https://www.flydubai.com/en/", "partner_url": null, "help_center_url": null, "updated_at": "2025-04-30T22:48:25.329631Z", "xpay_payment_enabled": false, "xpay_b2c_payment_enabled": false, "xpay_refund_enabled": false, "automatic_refund_by_xcore": false, "allow_policy_modifications_on_xcover": false, "emails": [{"id": "0deb181b-44d6-457c-979e-c62706256f85", "from_email": "XCover.com <<EMAIL>>", "bcc_email": "<EMAIL>", "email_slug": "travel-modification-flydubai", "email_version": 1, "event_name": 1, "include_partner_in_context": true, "attach_coi": true, "send_linked_sms": false, "auto_send": true, "enable_expression": ""}, {"id": "65856876-e1a2-406b-80ad-476fa8567da0", "from_email": "XCover.com <<EMAIL>>", "bcc_email": "<EMAIL>", "email_slug": "travel-cancellation-flydubai", "email_version": 1, "event_name": 2, "include_partner_in_context": true, "attach_coi": true, "send_linked_sms": false, "auto_send": true, "enable_expression": ""}, {"id": "e7a41d55-8263-403d-bb0f-142874d75eea", "from_email": "XCover.com <<EMAIL>>", "bcc_email": "<EMAIL>", "email_slug": "travel-confirmation-flydu<PERSON><PERSON>", "email_version": 1, "event_name": 0, "include_partner_in_context": true, "attach_coi": true, "send_linked_sms": false, "auto_send": true, "enable_expression": ""}], "attributes": {}, "signup_method_on_xcover": null, "use_standard_region": null, "allow_payout_customer": true, "subsidiary": {"id": "ZDTIY", "slug": "fly<PERSON><PERSON><PERSON>", "name": "fly<PERSON><PERSON><PERSON>", "title": "fly<PERSON><PERSON><PERSON>", "logo": "https://static.xcover.com/media/partnerlogos/2025/03/25/image_35_new_2.png", "contact_url": "https://www.flydubai.com/en/", "partner_url": null, "help_center_url": null, "updated_at": "2025-04-30T22:48:25.329631Z", "xpay_payment_enabled": false, "xpay_b2c_payment_enabled": false, "xpay_refund_enabled": false, "automatic_refund_by_xcore": false, "allow_policy_modifications_on_xcover": false, "emails": [{"id": "0deb181b-44d6-457c-979e-c62706256f85", "from_email": "XCover.com <<EMAIL>>", "bcc_email": "<EMAIL>", "email_slug": "travel-modification-flydubai", "email_version": 1, "event_name": 1, "include_partner_in_context": true, "attach_coi": true, "send_linked_sms": false, "auto_send": true, "enable_expression": ""}, {"id": "65856876-e1a2-406b-80ad-476fa8567da0", "from_email": "XCover.com <<EMAIL>>", "bcc_email": "<EMAIL>", "email_slug": "travel-cancellation-flydubai", "email_version": 1, "event_name": 2, "include_partner_in_context": true, "attach_coi": true, "send_linked_sms": false, "auto_send": true, "enable_expression": ""}, {"id": "e7a41d55-8263-403d-bb0f-142874d75eea", "from_email": "XCover.com <<EMAIL>>", "bcc_email": "<EMAIL>", "email_slug": "travel-confirmation-flydu<PERSON><PERSON>", "email_version": 1, "event_name": 0, "include_partner_in_context": true, "attach_coi": true, "send_linked_sms": false, "auto_send": true, "enable_expression": ""}], "attributes": {}, "signup_method_on_xcover": null, "use_standard_region": null, "allow_payout_customer": true}}, "partner_metadata": {}, "customer_language": "en"}